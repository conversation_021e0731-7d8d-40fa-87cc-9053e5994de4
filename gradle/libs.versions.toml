[versions]
dokka = "1.9.10"
kotlin = "2.1.20"
changelog = "2.2.1"
gradleIntelliJPlugin = "2.1.0"
qodana = "0.1.13"
kover = "0.7.5"

chapi = "2.1.2"

[libraries]


[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
dokka = { id = "org.jetbrains.dokka", version.ref = "dokka" }
gradleIntelliJPlugin = { id = "org.jetbrains.intellij.platform", version.ref = "gradleIntelliJPlugin" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }
serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }

[bundles]
