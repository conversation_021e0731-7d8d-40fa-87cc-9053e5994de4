<idea-plugin package="cc.unitmesh.python">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="com.intellij.modules.python"/>
    </dependencies>

    <extensions defaultExtensionNs="cc.unitmesh">
        <classContextBuilder language="Python"
                             implementationClass="cc.unitmesh.python.context.PythonClassContextBuilder"/>
        <fileContextBuilder language="Python"
                            implementationClass="cc.unitmesh.python.context.PythonFileContextBuilder"/>
        <methodContextBuilder language="Python"
                              implementationClass="cc.unitmesh.python.context.PythonMethodContextBuilder"/>
        <variableContextBuilder language="Python"
                                implementationClass="cc.unitmesh.python.context.PythonVariableContextBuilder"/>

        <livingDocumentation language="Python"
                             implementationClass="cc.unitmesh.python.provider.PythonLivingDocumentation"/>

        <chatContextProvider implementation="cc.unitmesh.python.PythonFrameworkContextProvider"/>

        <buildSystemProvider implementation="cc.unitmesh.python.provider.PythonBuildSystemProvider"/>

        <testContextProvider
                language="Python"
                implementation="cc.unitmesh.python.provider.PythonAutoTestService"/>

        <runService implementation="cc.unitmesh.python.provider.PythonRunService"/>

        <relatedClassProvider
                language="Python"
                implementationClass="cc.unitmesh.python.provider.PythonRelatedClassProvider"/>

        <langDictProvider implementation="cc.unitmesh.python.indexer.provider.PythonLangDictProvider"/>
    </extensions>
</idea-plugin>
