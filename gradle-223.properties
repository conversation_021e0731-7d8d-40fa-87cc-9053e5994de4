# Existent IDE versions can be found in the following repos:
# https://www.jetbrains.com/intellij-repository/releases/
# https://www.jetbrains.com/intellij-repository/snapshots/
# IC version has Javadoc / Kotlin docs, but no bundle for plugins, JavaScript, etc.
ideaVersion=IU-2022.3

# please see https://www.jetbrains.org/intellij/sdk/docs/basics/getting_started/build_number_ranges.html for description
pluginSinceBuild=223
pluginUntilBuild=232.*

platformPlugins=
mermaidPlugin=
plantUmlPlugin=PlantUML integration:6.5.0-IJ2022.2
pythonPlugin=PythonCore:223.7571.182
jupyterPlugin=

# check latest available version here https://plugins.jetbrains.com/plugin/8182--deprecated-rust
rustPlugin=org.rust.lang:0.4.185.5086-222

# https://plugins.jetbrains.com/plugin/9568-go/versions
goPlugin=org.jetbrains.plugins.go:223.7571.182
endpointsPlugin=
devContainerPlugin=

swaggerPlugin=com.intellij.swagger:223.7571.125
vuePlugin=org.jetbrains.plugins.vue:223.7571.176
openWritePlugin=
wechatPlugin=