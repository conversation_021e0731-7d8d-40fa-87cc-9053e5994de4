package cc.unitmesh.ide.javascript.provider

enum class JsWebFrameworks(val presentation: String, val packageName: String) {
    React("React", "react"),
    Vue("Vue", "vue"),
    Angular("Angular", "@angular/core"),
    AngularJS("AngularJS", "angular"),
    Svelte("Svelte", "svelte"),
    Astro("Astro", "astro"),
    Lit("Lit", "lit"),
    Solid("Solid", "solid-js"),
    Preact("Preact", "preact"),
    Next("Next", "next"),
    Nuxt("Nuxt", "nuxt"),
}

enum class JsTestFrameworks(val presentation: String, val packageName: String) {
    Jest("Jest", "jest"),
    <PERSON><PERSON>("<PERSON>cha", "mocha"),
    <PERSON>("<PERSON>", "jasmine"),
    <PERSON><PERSON>("Karma", "karma"),
    <PERSON>("Ava", "ava"),
    <PERSON><PERSON>("Tape", "tape"),
    Qunit("Qunit", "qunit"),
    Tap("Tap", "tap"),
    Cy<PERSON>("Cypress", "cypress"),
    Protractor("Protractor", "protractor"),
    Nightwatch("Nightwatch", "nightwatch"),
    Vitest("Vitest", "vitest")
}

private const val TYPESCRIPT_PACKAGE = "typescript"

val MOST_POPULAR_PACKAGES = setOf(
    "lodash",
    "request",
    "commander",
    "react",
    "express",
    "async",
    "moment",
    "prop-types",
    "react-dom",
    "bluebird",
    "underscore",
    "vue",
    "axios",
    "tslib",
    "glob",
    "yargs",
    "colors",
    "webpack",
    "uuid",
    "classnames",
    "minimist",
    "body-parser",
    "rxjs",
    "babel-runtime",
    "jquery",
    "babel-core",
    "core-js",
    "babel-loader",
    "cheerio",
    "rimraf",
    "eslint",
    "dotenv",
    TYPESCRIPT_PACKAGE,
    "@types/node",
    "@angular/core",
    "@angular/common",
    "redux",
    "gulp",
    "node-fetch",
    "@angular/platform-browser",
    "@babel/runtime",
    "handlebars",
    "@angular/compiler",
    "aws-sdk",
    "@angular/forms",
    "webpack-dev-server",
    "@angular/platform-browser-dynamic",
    "mocha",
    "socket.io",
    "ws",
    "node-sass",
    "@angular/router",
    "ramda",
    "react-redux",
    "@babel/core",
    "@angular/http",
    "ejs",
    "coffee-script",
    "mongodb",
    "chai",
    "mongoose",
    "xml2js",
    "bootstrap",
    "jest",
    "redis",
    "vue-router",
    "optimist",
    "promise",
    "@angular/animations",
    "postcss",
    "morgan",
    "less",
    "immutable"
)
