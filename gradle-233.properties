# Existent IDE versions can be found in the following repos:
# https://www.jetbrains.com/intellij-repository/releases/
# https://www.jetbrains.com/intellij-repository/snapshots/
# IC version has Javadoc / Kotlin docs, but no bundle for plugins, JavaScript, etc.
ideaVersion=IU-2023.3

# please see https://www.jetbrains.org/intellij/sdk/docs/basics/getting_started/build_number_ranges.html for description
pluginSinceBuild=233
pluginUntilBuild=233.*

platformPlugins = PlantUML integration:7.10.1-IJ2023.2,com.intellij.mermaid:0.0.22+IJ.232,intellij.jupyter:233.11799.196
#intellij.jupyter:233.11799.196
mermaidPlugin=com.intellij.mermaid:0.0.22+IJ.232
plantUmlPlugin=PlantUML integration:7.10.1-IJ2023.2
# check latest available version here https://plugins.jetbrains.com/plugin/22407-rust/versions
rustPlugin=com.jetbrains.rust:233.21799.284

pythonPlugin=PythonCore:233.11799.196
jupyterPlugin=intellij.jupyter:233.11799.196
devContainerPlugin=org.jetbrains.plugins.docker.gateway:233.11799.180

# https://plugins.jetbrains.com/plugin/9568-go/versions
goPlugin=org.jetbrains.plugins.go:233.11799.196
endpointsPlugin=com.intellij.microservices.ui:233.11799.172
swaggerPlugin=com.intellij.swagger:233.11799.188
vuePlugin=org.jetbrains.plugins.vue:233.11799.172
openWritePlugin=
wechatPlugin=