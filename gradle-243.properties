# Existent IDE versions can be found in the following repos:
# https://www.jetbrains.com/intellij-repository/releases/
# https://www.jetbrains.com/intellij-repository/snapshots/
# IC version has Javadoc / Kotlin docs, but no bundle for plugins, JavaScript, etc.
ideaVersion=IU-2024.3

# please see https://www.jetbrains.org/intellij/sdk/docs/basics/getting_started/build_number_ranges.html for description
pluginSinceBuild=243
pluginUntilBuild=252.*

platformPlugins=PlantUML integration:7.10.1-IJ2023.2,com.intellij.mermaid:0.0.24+IJ.243,git4Idea
mermaidPlugin=com.intellij.mermaid:0.0.22+IJ.232
plantUmlPlugin=PlantUML integration:7.10.1-IJ2023.2
# check latest available version here https://plugins.jetbrains.com/plugin/22407-rust/versions
rustPlugin=com.jetbrains.rust:243.21565.136

# https://plugins.jetbrains.com/plugin/9568-go/versions
goPlugin=org.jetbrains.plugins.go:243.21565.211

# https://plugins.jetbrains.com/plugin/7322-python-community-edition/versions
pythonPlugin=PythonCore:243.21565.211
jupyterPlugin=intellij.jupyter:243.21565.193
devContainerPlugin=org.jetbrains.plugins.docker.gateway:243.21565.122

endpointsPlugin=com.intellij.microservices.ui:243.21565.122
swaggerPlugin=com.intellij.swagger:243.21565.122
vuePlugin=org.jetbrains.plugins.vue:243.21565.135

openWritePlugin=com.intellij.openRewrite:243.21565.129
wechatPlugin=com.intellij.wechat.miniprogram:243.21565.120