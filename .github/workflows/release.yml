# GitHub Actions Workflow created for handling the release process based on the draft release prepared with the Build workflow.
# Running the publishPlugin task requires all following secrets to be provided: PUBLISH_TOKEN, PRIVATE_KEY, PRIVATE_KEY_PASSWORD, CERTIFICATE_CHAIN.
# See https://plugins.jetbrains.com/docs/intellij/plugin-signing.html for more information.

name: Release
on:
  release:
    types: [prereleased, released]

jobs:

  # Prepare and publish the plugin to JetBrains Marketplace repository
  release:
    name: Publish Plugin
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        platform-version: [223, 233, 241]
    env:
      ORG_GRADLE_PROJECT_platformVersion: ${{ matrix.platform-version }}

    permissions:
      contents: write
      pull-requests: write
    steps:

      # Check out current repository
      - name: Fetch Sources
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.release.tag_name }}

      # Set up Java environment for the next steps
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: zulu
          java-version: 17

      # Setup Gradle
      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-home-cache-cleanup: true

      # Set environment variables
      - name: Export Properties
        id: properties
        shell: bash
        run: |
          CHANGELOG="$(cat << 'EOM' | sed -e 's/^[[:space:]]*$//g' -e '/./,$!d'
          ${{ github.event.release.body }}
          EOM
          )"
          
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      # Update Unreleased section with the current release note
      - name: Patch Changelog
        if: ${{ steps.properties.outputs.changelog != '' }}
        env:
          CHANGELOG: ${{ steps.properties.outputs.changelog }}
        run: |
          ./gradlew patchChangelog --release-note="$CHANGELOG"

      # Publish the plugin to JetBrains Marketplace
      - name: Build Plugin
        run: ./gradlew buildPlugin

      # Upload artifact as a release asset
      - name: Upload Release Asset
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: gh release upload ${{ github.event.release.tag_name }} ./build/distributions/*
