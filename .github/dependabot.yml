# Dependabot configuration:
# https://docs.github.com/en/free-pro-team@latest/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  # Maintain dependencies for Gradle dependencies
  - package-ecosystem: "gradle"
    directory: "/"
    target-branch: "next"
    schedule:
      interval: "daily"
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    target-branch: "next"
    schedule:
      interval: "daily"
