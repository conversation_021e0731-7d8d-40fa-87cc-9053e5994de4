<idea-plugin package="cc.unitmesh" xmlns:xi="http://www.w3.org/2001/XInclude" allow-bundled-update="true">
    <id>cc.unitmesh.devti</id>
    <name>AutoDev</name>
    <vendor>Phodal Huang</vendor>

    <description><![CDATA[
    <a href="https://plugins.jetbrains.com/plugin/21520-autodev">Homepage</a> | <a href="https://github.com/unit-mesh/auto-dev">Github</a> | <a href="https://github.com/unit-mesh/auto-dev-vscode">VSCode Version</a> | <a href="https://github.com/unit-mesh/auto-dev/issues">Issues</a>.<br />
    <br/>
    AutoDev is a fully automated AI-assisted programming tool and an implementation of the DevTi Intellij IDE designed for use in-flight.
    ]]></description>

    <change-notes><![CDATA[
        todo
    ]]>
    </change-notes>

    <!-- please see https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html
         on how to target different products -->

    <xi:include href="/META-INF/autodev-core.xml" xpointer="xpointer(/idea-plugin/*)"/>

    <!--suppress PluginXmlValidity -->
    <content>
        <!-- language modules -->
        <module name="cc.unitmesh.pycharm"/>
        <module name="cc.unitmesh.idea"/>
        <module name="cc.unitmesh.kotlin"/>
        <module name="cc.unitmesh.javascript"/>
        <module name="cc.unitmesh.go"/>
        <module name="cc.unitmesh.rust"/>
<!--        <module name="cc.unitmesh.csharp"/>-->

        <!-- extension modules -->
        <module name="cc.unitmesh.database"/>
        <module name="cc.unitmesh.terminal"/>
        <module name="cc.unitmesh.mermaid"/>
        <module name="cc.unitmesh.plantuml"/>
        <module name="cc.unitmesh.git"/>
        <module name="cc.unitmesh.devti.language"/>
        <module name="cc.unitmesh.httpclient"/>
        <module name="cc.unitmesh.endpoints"/>
        <module name="cc.unitmesh.vue"/>
        <module name="cc.unitmesh.dependencies"/>
        <module name="cc.unitmesh.container"/>

        <module name="cc.unitmesh.diagram"/>
    </content>
</idea-plugin>
