<idea-plugin package="cc.unitmesh.go">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="org.jetbrains.plugins.go"/>
        <plugin id="com.intellij.modules.go-capable"/>
        <plugin id="com.intellij.modules.platform"/>
    </dependencies>

    <extensions defaultExtensionNs="cc.unitmesh">
        <fileContextBuilder language="go" implementationClass="cc.unitmesh.go.context.GoFileContextBuilder"/>
        <classContextBuilder language="go" implementationClass="cc.unitmesh.go.context.GoStructContextBuilder"/>
        <methodContextBuilder language="go" implementationClass="cc.unitmesh.go.context.GoMethodContextBuilder"/>
        <variableContextBuilder language="go" implementationClass="cc.unitmesh.go.context.GoVariableContextBuilder"/>

        <testContextProvider language="go" implementation="cc.unitmesh.go.provider.testing.GoAutoTestService"/>

        <chatContextProvider implementation="cc.unitmesh.go.provider.GoVersionChatContextProvider"/>

        <livingDocumentation language="go" implementationClass="cc.unitmesh.go.provider.GoLivingDocumentationProvider"/>

        <buildSystemProvider implementation="cc.unitmesh.go.provider.GoBuildSystemProvider"/>

        <langSketchProvider implementation="cc.unitmesh.go.provider.GoLangPlaygroundSketchProvider"/>

        <langDictProvider implementation="cc.unitmesh.go.indexer.provider.GoLangDictProvider"/>
    </extensions>
</idea-plugin>
