<idea-plugin package="cc.unitmesh.dependencies">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="org.jetbrains.security.package-checker"/>
    </dependencies>

    <actions>
        <action id="AutoDev.ToolWindow.Snippet.AutoDevDependenciesCheck"
                icon="AllIcons.Actions.DependencyAnalyzer"
                class="cc.unitmesh.dependencies.AutoDevDependenciesCheck">

            <add-to-group group-id="AutoDev.ToolWindow.Snippet.DependenciesToolbar" />
        </action>
    </actions>

<!--    <extensions defaultExtensionNs="com.intellij.packageChecker">-->
<!--        <dependencyModel implementation="cc.unitmesh.dependencies.AutoDevProjectDependenciesModel"/>-->
<!--    </extensions>-->

<!--  a posiable refs : https://github.com/redhat-developer/intellij-dependency-analytics  -->

    <extensions defaultExtensionNs="cc.unitmesh">
        <toolchainFunctionProvider implementation="cc.unitmesh.dependencies.DependenciesFunctionProvider"/>
    </extensions>
</idea-plugin>
