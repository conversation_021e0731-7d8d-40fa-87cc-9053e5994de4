# DevIns Lang: Enhancing AI Agent Capabilities

DevIns Lang, short for Development Intelligence Language, is the driving force behind our AutoDev AI Agent's advanced
capabilities. It serves as a comprehensive tool for defining and enhancing the behavior and functionalities of the AI
Agent.

## What is DevIns?

DevIns Lang empowers developers to craft intricate instructions for the AI Agent, enabling it to perform tasks with
greater intelligence and efficiency. By breaking down tasks into manageable instructions, developers can leverage the
full potential of the AI Agent in various scenarios.

### Key Features:

- **Enhanced Intelligence**: DevIns <PERSON> equips the AI Agent with a higher level of intelligence, enabling it to
  understand and execute complex instructions effectively.
- **Task Segmentation**: Complex AI tasks are divided into smaller, more manageable instructions, each defined using
  DevIns language within markdown files.

### Why DevIns?

Originally named DevInputLanguage in Issue [#101](https://github.com/unit-mesh/auto-dev/issues/101) for better
interaction with LLM, we later renamed it to DevIns. This change was prompted by the discovery of Devin AI, a renowned
AI company, to avoid confusion. The name DevIns, derived from DevInstruction, succinctly captures the essence of the
language in guiding the AI Agent's actions.

## AutoDev/Netscape Navigator

Return Code:

```devin
/symbol:cc.unitmesh.devti.language.lexer.DevInsLexer.parse
```

Hyperlink to the IDE:

```devin
/link:cc.unitmesh.devti.language.lexer.DevInsLexer.parse
```

L1C2 = Line 1 Column 2

```devin
/file:cpp/src/main/kotlin/cc/unitmesh/cpp/context/CppFileContextBuilder.kt#L1C2-L23
```
