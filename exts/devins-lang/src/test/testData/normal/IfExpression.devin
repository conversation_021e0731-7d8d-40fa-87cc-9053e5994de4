This is an expression that includes an if.
#if(1 > 0)
  ${language} if
#end


This is an expression that includes if and elseif.
#if($language.length() > 1)
  ${language} if
#elseif($language.length() > 5)
  ${language} elseif
#end


This is an expression that includes if and else.
#if($language.length() > 1 )
  ${language} if
#else
  ${language} else
#end


This is an expression that includes if,elseif and else.
#if($language.length() > 10 )
  ${language} if
#elseif($language.length() > 2 )
  ${language} elseif
#else
  ${language} else
#end


This is an expression that includes multiple if and elseif.
#if($language.length() > 1 )
  ${language} if
  #if($language.length() > 2 )
    ${language} if_if
  #elseif($language.length() > 20 )
    ${language} if_elseif
  #end
#elseif($language.length() > 10 )
  ${language} elseif
#end


This is an expression that includes multiple if and else.
#if($language.length() > 10 )
  ${language} if
#else
  ${language} else
  #if($language.length() > 2 )
    ${language} else_if
  #else
    ${language} else_else
  #end
#end


Please ignore the content and don't reply to me.