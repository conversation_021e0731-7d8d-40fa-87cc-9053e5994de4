DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.afterStreaming)('afterStreaming')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiElement(DevInTokenType.NEWLINE)('\n')
          PsiWhiteSpace('    ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInActionBodyImpl(ACTION_BODY)
              DevInActionExprImpl(ACTION_EXPR)
                DevInCaseBodyImpl(CASE_BODY)
                  DevInConditionFlagImpl(CONDITION_FLAG)
                    PsiElement(DevInTokenType.condition)('condition')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('      ')
                    DevInConditionStatementImpl(CONDITION_STATEMENT)
                      DevInCaseConditionImpl(CASE_CONDITION)
                        PsiElement(DevInTokenType.QUOTE_STRING)('"variable-success"')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.{)('{')
                      PsiWhiteSpace(' ')
                      DevInIneqComparisonExprImpl(INEQ_COMPARISON_EXPR)
                        DevInRefExprImpl(REF_EXPR)
                          DevInLiteralExprImpl(LITERAL_EXPR)
                            PsiElement(VARIABLE_START)('$')
                            PsiElement(DevInTokenType.IDENTIFIER)('selection')
                          PsiElement(DevInTokenType..)('.')
                          PsiElement(DevInTokenType.IDENTIFIER)('length')
                        PsiWhiteSpace(' ')
                        DevInIneqComparisonOpImpl(INEQ_COMPARISON_OP)
                          PsiElement(DevInTokenType.>)('>')
                        PsiWhiteSpace(' ')
                        DevInLiteralExprImpl(LITERAL_EXPR)
                          PsiElement(DevInTokenType.NUMBER)('1')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.})('}')
                      PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('      ')
                    DevInConditionStatementImpl(CONDITION_STATEMENT)
                      DevInCaseConditionImpl(CASE_CONDITION)
                        PsiElement(DevInTokenType.QUOTE_STRING)('"jsonpath-success"')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.{)('{')
                      PsiWhiteSpace(' ')
                      DevInCallExprImpl(CALL_EXPR)
                        DevInRefExprImpl(REF_EXPR)
                          PsiElement(DevInTokenType.IDENTIFIER)('jsonpath')
                        PsiElement(DevInTokenType.()('(')
                        DevInExpressionListImpl(EXPRESSION_LIST)
                          DevInLiteralExprImpl(LITERAL_EXPR)
                            PsiElement(DevInTokenType.QUOTE_STRING)('"/bookstore/book[price>35]"')
                        PsiElement(DevInTokenType.))(')')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.})('}')
                      PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('    ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  PsiElement(DevInTokenType.case)('case')
                  PsiWhiteSpace(' ')
                  PsiElement(DevInTokenType.condition)('condition')
                  PsiWhiteSpace(' ')
                  PsiElement(DevInTokenType.{)('{')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('      ')
                  DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                    DevInCaseConditionImpl(CASE_CONDITION)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"variable-sucesss"')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('done')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('      ')
                  DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                    DevInCaseConditionImpl(CASE_CONDITION)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"jsonpath-success"')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('task')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            <empty list>
                          PsiElement(DevInTokenType.))(')')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('      ')
                  DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                    DevInCaseConditionImpl(CASE_CONDITION)
                      PsiElement(DevInTokenType.default)('default')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('task')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            <empty list>
                          PsiElement(DevInTokenType.))(')')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  PsiElement(DevInTokenType.})('}')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
          PsiWhiteSpace('  ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')