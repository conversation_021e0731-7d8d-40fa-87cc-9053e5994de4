DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('name')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.QUOTE_STRING)('"Java to Kotlin"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('description')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.QUOTE_STRING)('"Convert Java to Kotlin file"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('interaction')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.IDENTIFIER)('AppendCursor')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('actionLocation')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.IDENTIFIER)('ContextMenu')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('enabled')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.BOOLEAN)('false')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('model')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.QUOTE_STRING)('"codegeex-4"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.onStreamingEnd)('onStreamingEnd')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace('  ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiWhiteSpace(' ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInActionBodyImpl(ACTION_BODY)
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('verifyCode')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.|)('|')
              PsiWhiteSpace(' ')
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('runCode')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Convert follow $language code to Kotlin')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('all')