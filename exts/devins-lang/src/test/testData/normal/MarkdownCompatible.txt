DevInFile
  PsiElement(DevInTokenType.TEXT_SEGMENT)('## Hello')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  CodeBlockElement(CODE)
    PsiElement(DevInTokenType.CODE_BLOCK_START)('```')
    PsiElement(DevInTokenType.LANGUAGE_ID)('shire')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    ASTWrapperPsiElement(CODE_CONTENTS)
      PsiElement(DevInTokenType.CODE_CONTENT)('---')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('name: "自动  patch"')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('variables:')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('  "codepath": /BlogController\.java/ { print }')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('  "controllerCode": /BlogController\.java/ { cat }')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('  "domainLanguage": /domain-language\.csv/ { cat }')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('onStreamingEnd: { parseCode | patch($codepath, $output) }')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('---')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.CODE_BLOCK_END)('```')