DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"extContext"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/build\.gradle\.kts/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('cat')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        ShireGrepFuncCall(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('grep')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"org.springframework.boot:spring-boot-starter-jdbc"')
                          PsiElement(DevInTokenType.))(')')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('print')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"This project use Spring Framework"')
                          PsiElement(DevInTokenType.))(')')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"testTemplate"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/\(.*\).java/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('    ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInCaseBodyImpl(CASE_BODY)
                          PsiElement(DevInTokenType.case)('case')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.QUOTE_STRING)('"$1"')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.{)('{')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"Controller"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/ControllerTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"Service"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/ServiceTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.default)('default')
                            PsiWhiteSpace('  ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/DefaultTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('    ')
                          PsiElement(DevInTokenType.})('}')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('  ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"allController"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFunctionStatementImpl(FUNCTION_STATEMENT)
                  PsiElement(DevInTokenType.{)('{')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  DevInFunctionBodyImpl(FUNCTION_BODY)
                    DevInQueryStatementImpl(QUERY_STATEMENT)
                      DevInFromClauseImpl(FROM_CLAUSE)
                        PsiElement(DevInTokenType.from)('from')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInPsiElementDeclImpl(PSI_ELEMENT_DECL)
                          DevInPsiVarDeclImpl(PSI_VAR_DECL)
                            DevInPsiTypeImpl(PSI_TYPE)
                              PsiElement(DevInTokenType.IDENTIFIER)('PsiClass')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiWhiteSpace(' ')
                          PsiComment(DevInTokenType.BLOCK_COMMENT)('/* sample */')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                      PsiWhiteSpace('    ')
                      DevInWhereClauseImpl(WHERE_CLAUSE)
                        PsiElement(DevInTokenType.where)('where')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInEqComparisonExprImpl(EQ_COMPARISON_EXPR)
                          DevInCallExprImpl(CALL_EXPR)
                            DevInRefExprImpl(REF_EXPR)
                              DevInRefExprImpl(REF_EXPR)
                                PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                              PsiElement(DevInTokenType..)('.')
                              PsiElement(DevInTokenType.IDENTIFIER)('getAnAnnotation')
                            PsiElement(DevInTokenType.()('(')
                            PsiElement(DevInTokenType.))(')')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.==)('==')
                          PsiWhiteSpace(' ')
                          DevInLiteralExprImpl(LITERAL_EXPR)
                            PsiElement(DevInTokenType.QUOTE_STRING)('"org.springframework.web.bind.annotation.RequestMapping"')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                      PsiWhiteSpace('    ')
                      DevInSelectClauseImpl(SELECT_CLAUSE)
                        PsiElement(DevInTokenType.select)('select')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInRefExprImpl(REF_EXPR)
                          DevInRefExprImpl(REF_EXPR)
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiElement(DevInTokenType..)('.')
                          PsiElement(DevInTokenType.IDENTIFIER)('id')
                        PsiElement(DevInTokenType.,)(',')
                        PsiWhiteSpace(' ')
                        DevInRefExprImpl(REF_EXPR)
                          DevInRefExprImpl(REF_EXPR)
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiElement(DevInTokenType..)('.')
                          PsiElement(DevInTokenType.IDENTIFIER)('name')
                        PsiElement(DevInTokenType.,)(',')
                        PsiWhiteSpace(' ')
                        DevInLiteralExprImpl(LITERAL_EXPR)
                          PsiElement(DevInTokenType.QUOTE_STRING)('"code"')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('  ')
                  PsiElement(DevInTokenType.})('}')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')