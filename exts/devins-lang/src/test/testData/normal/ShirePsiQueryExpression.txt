DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"allController"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFunctionStatementImpl(FUNCTION_STATEMENT)
                  PsiElement(DevInTokenType.{)('{')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  DevInFunctionBodyImpl(FUNCTION_BODY)
                    DevInQueryStatementImpl(QUERY_STATEMENT)
                      DevInFromClauseImpl(FROM_CLAUSE)
                        PsiElement(DevInTokenType.from)('from')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInPsiElementDeclImpl(PSI_ELEMENT_DECL)
                          DevInPsiVarDeclImpl(PSI_VAR_DECL)
                            DevInPsiTypeImpl(PSI_TYPE)
                              PsiElement(DevInTokenType.IDENTIFIER)('PsiClass')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiWhiteSpace(' ')
                          PsiComment(DevInTokenType.COMMENTS)('// the class')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                      PsiWhiteSpace('    ')
                      DevInWhereClauseImpl(WHERE_CLAUSE)
                        PsiElement(DevInTokenType.where)('where')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInLogicalAndExprImpl(LOGICAL_AND_EXPR)
                          DevInCallExprImpl(CALL_EXPR)
                            DevInRefExprImpl(REF_EXPR)
                              DevInRefExprImpl(REF_EXPR)
                                PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                              PsiElement(DevInTokenType..)('.')
                              PsiElement(DevInTokenType.IDENTIFIER)('extends')
                            PsiElement(DevInTokenType.()('(')
                            DevInExpressionListImpl(EXPRESSION_LIST)
                              DevInLiteralExprImpl(LITERAL_EXPR)
                                PsiElement(DevInTokenType.QUOTE_STRING)('"org.springframework.web.bind.annotation.RestController"')
                            PsiElement(DevInTokenType.))(')')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.and)('and')
                          PsiWhiteSpace(' ')
                          DevInEqComparisonExprImpl(EQ_COMPARISON_EXPR)
                            DevInCallExprImpl(CALL_EXPR)
                              DevInRefExprImpl(REF_EXPR)
                                DevInRefExprImpl(REF_EXPR)
                                  PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                                PsiElement(DevInTokenType..)('.')
                                PsiElement(DevInTokenType.IDENTIFIER)('getAnAnnotation')
                              PsiElement(DevInTokenType.()('(')
                              PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.==)('==')
                            PsiWhiteSpace(' ')
                            DevInLiteralExprImpl(LITERAL_EXPR)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"org.springframework.web.bind.annotation.RequestMapping"')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                      PsiWhiteSpace('    ')
                      DevInSelectClauseImpl(SELECT_CLAUSE)
                        PsiElement(DevInTokenType.select)('select')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInRefExprImpl(REF_EXPR)
                          DevInRefExprImpl(REF_EXPR)
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiElement(DevInTokenType..)('.')
                          PsiElement(DevInTokenType.IDENTIFIER)('id')
                        PsiElement(DevInTokenType.,)(',')
                        PsiWhiteSpace(' ')
                        DevInRefExprImpl(REF_EXPR)
                          DevInRefExprImpl(REF_EXPR)
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiElement(DevInTokenType..)('.')
                          PsiElement(DevInTokenType.IDENTIFIER)('name')
                        PsiElement(DevInTokenType.,)(',')
                        PsiWhiteSpace(' ')
                        DevInLiteralExprImpl(LITERAL_EXPR)
                          PsiElement(DevInTokenType.QUOTE_STRING)('"code"')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('  ')
                  PsiElement(DevInTokenType.})('}')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('allController')