DevInFile
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('rename')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' com.phodal.shirelang.run.ShireProgramRunner to com.phodal.shirelang.run.ShireProgramRunnerImpl')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('safeDelete')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' com.phodal.shirelang.run.ShireProgramRunnerImpl')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('delete')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' com.phodal.shirelang.run.ShireProgramRunnerImpl')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('move')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' com.phodal.shirelang.ShireProgramRunner to com.phodal.shirelang.run.ShireProgramRunner')