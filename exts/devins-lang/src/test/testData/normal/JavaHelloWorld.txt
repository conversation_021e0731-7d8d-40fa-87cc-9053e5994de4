DevInFile
  PsiElement(DevInTokenType.TEXT_SEGMENT)('解释如下的代码：')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  CodeBlockElement(CODE)
    PsiElement(DevInTokenType.CODE_BLOCK_START)('```')
    PsiElement(DevInTokenType.LANGUAGE_ID)('java')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    ASTWrapperPsiElement(CODE_CONTENTS)
      PsiElement(DevInTokenType.CODE_CONTENT)('public class Main {')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('    public static void main(String[] args) {')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('        System.out.println("Hello, world!");')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('    }')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('}')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.CODE_BLOCK_END)('```')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('请使用 Markdown 语法返回。')