DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.when)('when')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiWhiteSpace(' ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInLogicalAndExprImpl(LOGICAL_AND_EXPR)
              DevInEqComparisonExprImpl(EQ_COMPARISON_EXPR)
                DevInRefExprImpl(REF_EXPR)
                  DevInLiteralExprImpl(LITERAL_EXPR)
                    PsiElement(VARIABLE_START)('$')
                    PsiElement(DevInTokenType.IDENTIFIER)('selection')
                  PsiElement(DevInTokenType..)('.')
                  PsiElement(DevInTokenType.IDENTIFIER)('length')
                PsiWhiteSpace(' ')
                PsiElement(DevInTokenType.==)('==')
                PsiWhiteSpace(' ')
                DevInLiteralExprImpl(LITERAL_EXPR)
                  PsiElement(DevInTokenType.NUMBER)('1')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.&&)('&&')
              PsiWhiteSpace(' ')
              DevInEqComparisonExprImpl(EQ_COMPARISON_EXPR)
                DevInCallExprImpl(CALL_EXPR)
                  DevInRefExprImpl(REF_EXPR)
                    DevInLiteralExprImpl(LITERAL_EXPR)
                      PsiElement(VARIABLE_START)('$')
                      PsiElement(DevInTokenType.IDENTIFIER)('selection')
                    PsiElement(DevInTokenType..)('.')
                    PsiElement(DevInTokenType.IDENTIFIER)('first')
                  PsiElement(DevInTokenType.()('(')
                  PsiElement(DevInTokenType.))(')')
                PsiWhiteSpace(' ')
                PsiElement(DevInTokenType.==)('==')
                PsiWhiteSpace(' ')
                DevInLiteralExprImpl(LITERAL_EXPR)
                  PsiElement(DevInTokenType.QUOTE_STRING)(''file'')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')