DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          PsiElement(DevInTokenType.functions)('functions')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  DevInFrontMatterIdImpl(FRONT_MATTER_ID)
                    PsiElement(DevInTokenType.IDENTIFIER)('aFunc')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInForeignFunctionImpl(FOREIGN_FUNCTION)
                  DevInForeignPathImpl(FOREIGN_PATH)
                    PsiElement(DevInTokenType.QUOTE_STRING)('"defaultMain.py"')
                  PsiElement(DevInTokenType.()('(')
                  DevInForeignTypeImpl(FOREIGN_TYPE)
                    PsiElement(DevInTokenType.IDENTIFIER)('string')
                  PsiElement(DevInTokenType.))(')')
                PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  DevInFrontMatterIdImpl(FRONT_MATTER_ID)
                    PsiElement(DevInTokenType.IDENTIFIER)('aFunc')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInForeignFunctionImpl(FOREIGN_FUNCTION)
                  DevInForeignPathImpl(FOREIGN_PATH)
                    PsiElement(DevInTokenType.QUOTE_STRING)('"multipleOutput.py"')
                  PsiElement(DevInTokenType.()('(')
                  DevInForeignTypeImpl(FOREIGN_TYPE)
                    PsiElement(DevInTokenType.IDENTIFIER)('string')
                  PsiElement(DevInTokenType.,)(',')
                  PsiWhiteSpace(' ')
                  DevInForeignTypeImpl(FOREIGN_TYPE)
                    PsiElement(DevInTokenType.IDENTIFIER)('number')
                  PsiElement(DevInTokenType.))(')')
                  PsiWhiteSpace(' ')
                  PsiElement(DevInTokenType.PROCESS)('->')
                  PsiWhiteSpace(' ')
                  DevInForeignOutputImpl(FOREIGN_OUTPUT)
                    DevInOutputVarImpl(OUTPUT_VAR)
                      PsiElement(DevInTokenType.IDENTIFIER)('content')
                PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  DevInFrontMatterIdImpl(FRONT_MATTER_ID)
                    PsiElement(DevInTokenType.IDENTIFIER)('cFunc')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInForeignFunctionImpl(FOREIGN_FUNCTION)
                  DevInForeignPathImpl(FOREIGN_PATH)
                    PsiElement(DevInTokenType.QUOTE_STRING)('"accessFunctionIfSupport.py"')
                  PsiElement(DevInTokenType.::)('::')
                  DevInForeignFuncNameImpl(FOREIGN_FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('resize')
                  PsiElement(DevInTokenType.()('(')
                  DevInForeignTypeImpl(FOREIGN_TYPE)
                    PsiElement(DevInTokenType.IDENTIFIER)('string')
                  PsiElement(DevInTokenType.,)(',')
                  PsiWhiteSpace(' ')
                  DevInForeignTypeImpl(FOREIGN_TYPE)
                    PsiElement(DevInTokenType.IDENTIFIER)('number')
                  PsiElement(DevInTokenType.,)(',')
                  PsiWhiteSpace(' ')
                  DevInForeignTypeImpl(FOREIGN_TYPE)
                    PsiElement(DevInTokenType.IDENTIFIER)('number')
                  PsiElement(DevInTokenType.))(')')
                  PsiWhiteSpace(' ')
                  PsiElement(DevInTokenType.PROCESS)('->')
                  PsiWhiteSpace(' ')
                  DevInForeignOutputImpl(FOREIGN_OUTPUT)
                    DevInOutputVarImpl(OUTPUT_VAR)
                      PsiElement(DevInTokenType.IDENTIFIER)('image')
                PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')