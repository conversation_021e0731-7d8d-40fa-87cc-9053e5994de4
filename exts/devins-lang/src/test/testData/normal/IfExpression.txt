DevInFile
  PsiElement(DevInTokenType.TEXT_SEGMENT)('This is an expression that includes an if.')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#if(1 > 0)')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiErrorElement:<used>, DevInTokenType.#, DevInTokenType.CODE_BLOCK_START, DevInTokenType.CONTENT_COMMENTS, DevInTokenType.NEWLINE or DevInTokenType.TEXT_SEGMENT expected
    PsiElement(VARIABLE_START)(' ')
  PsiElement(DUMMY_BLOCK)
    PsiElement(DUMMY_BLOCK)
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('This is an expression that includes if and elseif.')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($language.length() > 1)')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#elseif($language.length() > 5)')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' elseif')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('This is an expression that includes if and else.')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($language.length() > 1 )')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#else')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('This is an expression that includes if,elseif and else.')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($language.length() > 10 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#elseif($language.length() > 2 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' elseif')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('This is an expression that includes multiple if and elseif.')
  PsiElement(DUMMY_BLOCK)
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($language.length() > 1 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('  #if($language.length() > 2 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if_if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('  #elseif($language.length() > 20 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if_elseif')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('  #end')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#elseif($language.length() > 10 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
    PsiElement(DUMMY_BLOCK)
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' elseif')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('This is an expression that includes multiple if and else.')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($language.length() > 10 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('  #if($language.length() > 2 )')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
    PsiElement(DUMMY_BLOCK)
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' else_if')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('  #else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)(' ')
    PsiElement(DUMMY_BLOCK)
      PsiElement(VARIABLE_START)(' ')
      PsiElement(VARIABLE_START)('$')
      PsiElement(DevInTokenType.{)('{')
      PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
      PsiElement(DevInTokenType.TEXT_SEGMENT)(' else_else')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('  #end')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Please ignore the content and don't reply to me.')