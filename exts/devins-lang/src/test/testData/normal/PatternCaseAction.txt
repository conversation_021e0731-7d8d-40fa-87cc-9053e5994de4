DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"var1"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"demo"')
                PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"var2"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/.*.java/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        ShireGrepFuncCall(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('grep')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"error.log"')
                          PsiElement(DevInTokenType.))(')')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('sort')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('xargs')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"rm"')
                          PsiElement(DevInTokenType.))(')')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"var3"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/.*.log/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('    ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInCaseBodyImpl(CASE_BODY)
                          PsiElement(DevInTokenType.case)('case')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.QUOTE_STRING)('"$0"')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.{)('{')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"error"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                ShireGrepFuncCall(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('grep')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"ERROR"')
                                  PsiElement(DevInTokenType.))(')')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('sort')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('xargs')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"notify_admin"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"warn"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                ShireGrepFuncCall(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('grep')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"WARN"')
                                  PsiElement(DevInTokenType.))(')')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('sort')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('xargs')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"notify_admin"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"info"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                ShireGrepFuncCall(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('grep')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"INFO"')
                                  PsiElement(DevInTokenType.))(')')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('sort')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('xargs')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"notify_user"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.default)('default')
                            PsiWhiteSpace('  ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                ShireGrepFuncCall(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('grep')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"ERROR"')
                                  PsiElement(DevInTokenType.))(')')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('sort')
                              PsiWhiteSpace(' ')
                              PsiElement(DevInTokenType.|)('|')
                              PsiWhiteSpace(' ')
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('xargs')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('"notify_admin"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('    ')
                          PsiElement(DevInTokenType.})('}')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('  ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"var4"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
                  PsiElement(DevInTokenType.NUMBER)('42')
                PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('var1')