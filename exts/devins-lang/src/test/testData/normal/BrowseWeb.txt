DevInFile
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('browse')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('https://ide.unitmesh.cc')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('browse')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('https://www.example.com/page?param1=value1&param2=value2')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('browse')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('https://www.example.com/page#section1')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('browse')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('http://localhost:3000/page')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('browse')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('https://username:<EMAIL>/page')