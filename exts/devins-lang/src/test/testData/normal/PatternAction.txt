DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"var1"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"value2"')
                PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"var2"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/.*.java/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        ShireGrepFuncCall(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('grep')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"error.log"')
                          PsiElement(DevInTokenType.))(')')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('sort')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('xargs')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"rm"')
                          PsiElement(DevInTokenType.))(')')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('var1')