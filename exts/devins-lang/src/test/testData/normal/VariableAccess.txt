DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.when)('when')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInLogicalAndExprImpl(LOGICAL_AND_EXPR)
          DevInEqComparisonExprImpl(EQ_COMPARISON_EXPR)
            DevInRefExprImpl(REF_EXPR)
              DevInLiteralExprImpl(LITERAL_EXPR)
                PsiElement(VARIABLE_START)('$')
                PsiElement(DevInTokenType.IDENTIFIER)('selection')
              PsiElement(DevInTokenType..)('.')
              PsiElement(DevInTokenType.IDENTIFIER)('length')
            PsiWhiteSpace(' ')
            PsiElement(DevInTokenType.==)('==')
            PsiWhiteSpace(' ')
            DevInLiteralExprImpl(LITERAL_EXPR)
              PsiElement(DevInTokenType.NUMBER)('1')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.&&)('&&')
          PsiWhiteSpace(' ')
          DevInEqComparisonExprImpl(EQ_COMPARISON_EXPR)
            DevInCallExprImpl(CALL_EXPR)
              DevInRefExprImpl(REF_EXPR)
                DevInLiteralExprImpl(LITERAL_EXPR)
                  PsiElement(VARIABLE_START)('$')
                  PsiElement(DevInTokenType.IDENTIFIER)('selection')
                PsiElement(DevInTokenType..)('.')
                PsiElement(DevInTokenType.IDENTIFIER)('first')
              PsiElement(DevInTokenType.()('(')
              PsiElement(DevInTokenType.))(')')
            PsiWhiteSpace(' ')
            PsiElement(DevInTokenType.==)('==')
            PsiWhiteSpace(' ')
            DevInLiteralExprImpl(LITERAL_EXPR)
              PsiElement(DevInTokenType.QUOTE_STRING)(''file'')
        PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Write unit test for following ${context.lang} code.')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('frameworkContext')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($context.relatedClasses.length() > 0 )')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Here is the relate code maybe you can use')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('relatedClasses')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  CodeBlockElement(CODE)
    PsiElement(DevInTokenType.CODE_BLOCK_START)('```')
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInRefExprImpl(REF_EXPR)
      DevInRefExprImpl(REF_EXPR)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      PsiElement(DevInTokenType.IDENTIFIER)('lang')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    ASTWrapperPsiElement(CODE_CONTENTS)
      PsiElement(DevInTokenType.CODE_CONTENT)('${context.imports}')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('${context.sourceCode}')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.CODE_BLOCK_END)('```')