DevInFile
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('explain')
  PsiErrorElement:<used>, DevInTokenType.#, DevInTokenType.CODE_BLOCK_START, DevInTokenType.COLON, DevInTokenType.CONTENT_COMMENTS, DevInTokenType.NEWLINE or DevInTokenType.TEXT_SEGMENT expected
    PsiElement(COMMAND_START)(' ')
  PsiElement(DUMMY_BLOCK)
    PsiElement(COMMAND_START)('/')
    PsiElement(DevInTokenType.IDENTIFIER)('symbol')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('cc.unitmesh.devti#RevProvider.constructor')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(COMMAND_START)('/')
    PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(COMMAND_START)(' ')
    PsiElement(COMMAND_START)('/')
  PsiElement(DevInTokenType.IDENTIFIER)('symbol')
  PsiElement(DevInTokenType.COLON)(':')
  PsiElement(DevInTokenType.COMMAND_PROP)('cc.unitmesh.devti#RevProvider.completions')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(COMMAND_START)('/')
  PsiElement(DevInTokenType.IDENTIFIER)('write')
  PsiElement(DevInTokenType.COLON)(':')
  PsiElement(DevInTokenType.COMMAND_PROP)('presentation/VirtualFilePresentation.java#L1-L12')