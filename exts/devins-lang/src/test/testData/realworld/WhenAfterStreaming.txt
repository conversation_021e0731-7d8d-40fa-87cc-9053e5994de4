DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('interaction')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.IDENTIFIER)('AppendCursor')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.onStreamingEnd)('onStreamingEnd')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiWhiteSpace(' ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInActionBodyImpl(ACTION_BODY)
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('parseCode')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"json"')
                  PsiElement(DevInTokenType.))(')')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.|)('|')
              PsiWhiteSpace(' ')
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('verifyCode')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"json"')
                  PsiElement(DevInTokenType.))(')')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.|)('|')
              PsiWhiteSpace(' ')
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('runCode')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"json"')
                  PsiElement(DevInTokenType.))(')')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.when)('when')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInLiteralExprImpl(LITERAL_EXPR)
          PsiElement(DevInTokenType.QUOTE_STRING)('"$selection.length == 1 && $selection.first() == 'file'"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('hi')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Hello! How can I assist you today?')