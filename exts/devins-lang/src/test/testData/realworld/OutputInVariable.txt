DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('name')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.QUOTE_STRING)('"代码修改"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"controllerCode"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/any/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('cat')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              DevInVariableStartImpl(VARIABLE_START)
                                PsiElement(VARIABLE_START)('$')
                              DevInVariableIdImpl(VARIABLE_ID)
                                PsiElement(DevInTokenType.IDENTIFIER)('output')
                          PsiElement(DevInTokenType.))(')')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.onStreamingEnd)('onStreamingEnd')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiWhiteSpace(' ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInActionBodyImpl(ACTION_BODY)
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('parseCode')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.|)('|')
              PsiWhiteSpace(' ')
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('saveFile')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      DevInVariableStartImpl(VARIABLE_START)
                        PsiElement(VARIABLE_START)('$')
                      DevInVariableIdImpl(VARIABLE_ID)
                        PsiElement(DevInTokenType.IDENTIFIER)('output')
                  PsiElement(DevInTokenType.))(')')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('output')