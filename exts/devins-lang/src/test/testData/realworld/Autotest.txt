DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('name')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.QUOTE_STRING)('"AutoTest"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('description')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.QUOTE_STRING)('"AutoTest"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('interaction')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.IDENTIFIER)('AppendCursor')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('actionLocation')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.IDENTIFIER)('ContextMenu')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.when)('when')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInLogicalAndExprImpl(LOGICAL_AND_EXPR)
          DevInCallExprImpl(CALL_EXPR)
            DevInRefExprImpl(REF_EXPR)
              DevInLiteralExprImpl(LITERAL_EXPR)
                PsiElement(VARIABLE_START)('$')
                PsiElement(DevInTokenType.IDENTIFIER)('fileName')
              PsiElement(DevInTokenType..)('.')
              PsiElement(DevInTokenType.IDENTIFIER)('contains')
            PsiElement(DevInTokenType.()('(')
            DevInExpressionListImpl(EXPRESSION_LIST)
              DevInLiteralExprImpl(LITERAL_EXPR)
                PsiElement(DevInTokenType.QUOTE_STRING)('".java"')
            PsiElement(DevInTokenType.))(')')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.&&)('&&')
          PsiWhiteSpace(' ')
          DevInCallExprImpl(CALL_EXPR)
            DevInRefExprImpl(REF_EXPR)
              DevInLiteralExprImpl(LITERAL_EXPR)
                PsiElement(VARIABLE_START)('$')
                PsiElement(DevInTokenType.IDENTIFIER)('filePath')
              PsiElement(DevInTokenType..)('.')
              PsiElement(DevInTokenType.IDENTIFIER)('contains')
            PsiElement(DevInTokenType.()('(')
            DevInExpressionListImpl(EXPRESSION_LIST)
              DevInLiteralExprImpl(LITERAL_EXPR)
                PsiElement(DevInTokenType.QUOTE_STRING)('"src/main/java"')
            PsiElement(DevInTokenType.))(')')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('fileName-rules')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/.*Controller.java/')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"When testing controller, you MUST use MockMvc and test API only."')
                PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"extContext"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/build\.gradle\.kts/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('cat')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        ShireGrepFuncCall(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('grep')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"org.springframework.boot:spring-boot-starter-jdbc"')
                          PsiElement(DevInTokenType.))(')')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.|)('|')
                      PsiWhiteSpace(' ')
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('print')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            DevInPipelineArgImpl(PIPELINE_ARG)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"This project use Spring Framework"')
                          PsiElement(DevInTokenType.))(')')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"testTemplate"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/\(.*\).java/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('    ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInCaseBodyImpl(CASE_BODY)
                          PsiElement(DevInTokenType.case)('case')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.QUOTE_STRING)('"$1"')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.{)('{')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"Controller"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/ControllerTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"Service"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/ServiceTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.default)('default')
                            PsiWhiteSpace('  ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/DefaultTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('    ')
                          PsiElement(DevInTokenType.})('}')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('  ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"allController"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInFunctionStatementImpl(FUNCTION_STATEMENT)
                  PsiElement(DevInTokenType.{)('{')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  DevInFunctionBodyImpl(FUNCTION_BODY)
                    DevInQueryStatementImpl(QUERY_STATEMENT)
                      DevInFromClauseImpl(FROM_CLAUSE)
                        PsiElement(DevInTokenType.from)('from')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInPsiElementDeclImpl(PSI_ELEMENT_DECL)
                          DevInPsiVarDeclImpl(PSI_VAR_DECL)
                            DevInPsiTypeImpl(PSI_TYPE)
                              PsiElement(DevInTokenType.IDENTIFIER)('PsiClass')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                          PsiWhiteSpace(' ')
                          PsiComment(DevInTokenType.BLOCK_COMMENT)('/* sample */')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                      PsiWhiteSpace('    ')
                      DevInWhereClauseImpl(WHERE_CLAUSE)
                        PsiElement(DevInTokenType.where)('where')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInIneqComparisonExprImpl(INEQ_COMPARISON_EXPR)
                          DevInCallExprImpl(CALL_EXPR)
                            DevInRefExprImpl(REF_EXPR)
                              DevInCallExprImpl(CALL_EXPR)
                                DevInRefExprImpl(REF_EXPR)
                                  DevInRefExprImpl(REF_EXPR)
                                    PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                                  PsiElement(DevInTokenType..)('.')
                                  PsiElement(DevInTokenType.IDENTIFIER)('getMethods')
                                PsiElement(DevInTokenType.()('(')
                                PsiElement(DevInTokenType.))(')')
                              PsiElement(DevInTokenType..)('.')
                              PsiElement(DevInTokenType.IDENTIFIER)('length')
                            PsiElement(DevInTokenType.()('(')
                            PsiElement(DevInTokenType.))(')')
                          PsiWhiteSpace(' ')
                          DevInIneqComparisonOpImpl(INEQ_COMPARISON_OP)
                            PsiElement(DevInTokenType.>)('>')
                          PsiWhiteSpace(' ')
                          DevInLiteralExprImpl(LITERAL_EXPR)
                            PsiElement(DevInTokenType.NUMBER)('0')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                      PsiWhiteSpace('    ')
                      DevInSelectClauseImpl(SELECT_CLAUSE)
                        PsiElement(DevInTokenType.select)('select')
                        PsiWhiteSpace(' ')
                        PsiElement(DevInTokenType.{)('{')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('        ')
                        DevInCallExprImpl(CALL_EXPR)
                          DevInRefExprImpl(REF_EXPR)
                            DevInRefExprImpl(REF_EXPR)
                              PsiElement(DevInTokenType.IDENTIFIER)('clazz')
                            PsiElement(DevInTokenType..)('.')
                            PsiElement(DevInTokenType.IDENTIFIER)('getMethods')
                          PsiElement(DevInTokenType.()('(')
                          PsiElement(DevInTokenType.))(')')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                        PsiWhiteSpace('    ')
                        PsiElement(DevInTokenType.})('}')
                        PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('  ')
                  PsiElement(DevInTokenType.})('}')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Write unit test for following ${context.language} code.')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('frameworkContext')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($context.relatedClasses.length() > 0 )')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Here is the relate code maybe you can use')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('relatedClasses')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($context.currentClassName.length() > 0 )')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('This is the class where the source code resides:')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('currentClassCode')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('extContext')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Here is the imports to help you understand:')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVarAccessImpl(VAR_ACCESS)
      PsiElement(DevInTokenType.{)('{')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('context')
      PsiElement(DevInTokenType..)('.')
      DevInVariableIdImpl(VARIABLE_ID)
        PsiElement(DevInTokenType.IDENTIFIER)('imports')
      PsiElement(DevInTokenType.})('}')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Here is the source code to be tested:')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  CodeBlockElement(CODE)
    PsiElement(DevInTokenType.CODE_BLOCK_START)('```')
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableExprImpl(VARIABLE_EXPR)
      PsiElement(DevInTokenType.{)('{')
      DevInRefExprImpl(REF_EXPR)
        DevInRefExprImpl(REF_EXPR)
          PsiElement(DevInTokenType.IDENTIFIER)('context')
        PsiElement(DevInTokenType..)('.')
        PsiElement(DevInTokenType.IDENTIFIER)('language')
      PsiElement(DevInTokenType.})('}')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    ASTWrapperPsiElement(CODE_CONTENTS)
      PsiElement(DevInTokenType.CODE_CONTENT)('${context.selection}')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.CODE_BLOCK_END)('```')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#if($context.isNewFile)')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Should include package and imports. Start method test code with Markdown code block here:')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#else')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('Should include package and imports. Start ${context.targetTestFileName} test code with Markdown code block here:')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#end')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('allController')