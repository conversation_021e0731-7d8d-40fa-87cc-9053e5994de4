DevInFile
  DevInFrontMatterHeaderImpl(FRONT_MATTER_HEADER)
    PsiElement(DevInTokenType.FRONTMATTER_START)('---')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    DevInFrontMatterEntriesImpl(FRONT_MATTER_ENTRIES)
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.when)('when')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInLiteralExprImpl(LITERAL_EXPR)
          PsiElement(DevInTokenType.QUOTE_STRING)('"xxx"')
        PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
          DevInFrontMatterIdImpl(FRONT_MATTER_ID)
            PsiElement(DevInTokenType.IDENTIFIER)('variables')
        PsiElement(DevInTokenType.COLON)(':')
        DevInFrontMatterValueImpl(FRONT_MATTER_VALUE)
          PsiElement(DevInTokenType.NEWLINE)('\n')
          DevInObjectKeyValueImpl(OBJECT_KEY_VALUE)
            PsiElement(DevInTokenType.INDENT)('  ')
            DevInKeyValueImpl(KEY_VALUE)
              DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
                DevInFrontMatterKeyImpl(FRONT_MATTER_KEY)
                  PsiElement(DevInTokenType.QUOTE_STRING)('"testTemplate"')
                PsiElement(DevInTokenType.COLON)(':')
                PsiWhiteSpace(' ')
                DevInPatternActionImpl(PATTERN_ACTION)
                  PatternElement(PATTERN)
                    PsiElement(DevInTokenType.PATTERN_EXPR)('/\(.*\).java/')
                  PsiWhiteSpace(' ')
                  DevInActionBlockImpl(ACTION_BLOCK)
                    PsiElement(DevInTokenType.{)('{')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('    ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInCaseBodyImpl(CASE_BODY)
                          PsiElement(DevInTokenType.case)('case')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.QUOTE_STRING)('"$1"')
                          PsiWhiteSpace(' ')
                          PsiElement(DevInTokenType.{)('{')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"Controller"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/ControllerTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.QUOTE_STRING)('"Service"')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/ServiceTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('      ')
                          DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                            DevInCaseConditionImpl(CASE_CONDITION)
                              PsiElement(DevInTokenType.default)('default')
                            PsiWhiteSpace('  ')
                            PsiElement(DevInTokenType.{)('{')
                            PsiWhiteSpace(' ')
                            DevInActionBodyImpl(ACTION_BODY)
                              DevInActionExprImpl(ACTION_EXPR)
                                DevInFuncCallImpl(FUNC_CALL)
                                  DevInFuncNameImpl(FUNC_NAME)
                                    PsiElement(DevInTokenType.IDENTIFIER)('cat')
                                  PsiElement(DevInTokenType.()('(')
                                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                                    DevInPipelineArgImpl(PIPELINE_ARG)
                                      PsiElement(DevInTokenType.QUOTE_STRING)('".shire/templates/DefaultTest.java"')
                                  PsiElement(DevInTokenType.))(')')
                            PsiWhiteSpace(' ')
                            PsiElement(DevInTokenType.})('}')
                            PsiElement(DevInTokenType.NEWLINE)('\n')
                          PsiWhiteSpace('    ')
                          PsiElement(DevInTokenType.})('}')
                          PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('  ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.onStreaming)('onStreaming')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiWhiteSpace(' ')
          PsiComment(DevInTokenType.BLOCK_COMMENT)('/* functions */')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.onStreamingEnd)('onStreamingEnd')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiWhiteSpace(' ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInActionBodyImpl(ACTION_BODY)
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('parseCode')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"json"')
                  PsiElement(DevInTokenType.))(')')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.|)('|')
              PsiWhiteSpace(' ')
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('verifyCode')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"json"')
                  PsiElement(DevInTokenType.))(')')
              PsiWhiteSpace(' ')
              PsiElement(DevInTokenType.|)('|')
              PsiWhiteSpace(' ')
              DevInActionExprImpl(ACTION_EXPR)
                DevInFuncCallImpl(FUNC_CALL)
                  DevInFuncNameImpl(FUNC_NAME)
                    PsiElement(DevInTokenType.IDENTIFIER)('runCode')
                  PsiElement(DevInTokenType.()('(')
                  DevInPipelineArgsImpl(PIPELINE_ARGS)
                    DevInPipelineArgImpl(PIPELINE_ARG)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"json"')
                  PsiElement(DevInTokenType.))(')')
          PsiWhiteSpace(' ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
      DevInFrontMatterEntryImpl(FRONT_MATTER_ENTRY)
        DevInLifecycleIdImpl(LIFECYCLE_ID)
          PsiElement(DevInTokenType.afterStreaming)('afterStreaming')
        PsiElement(DevInTokenType.COLON)(':')
        PsiWhiteSpace(' ')
        DevInFunctionStatementImpl(FUNCTION_STATEMENT)
          PsiElement(DevInTokenType.{)('{')
          PsiElement(DevInTokenType.NEWLINE)('\n')
          PsiWhiteSpace('    ')
          DevInFunctionBodyImpl(FUNCTION_BODY)
            DevInActionBodyImpl(ACTION_BODY)
              DevInActionExprImpl(ACTION_EXPR)
                DevInCaseBodyImpl(CASE_BODY)
                  DevInConditionFlagImpl(CONDITION_FLAG)
                    PsiElement(DevInTokenType.condition)('condition')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('      ')
                    DevInConditionStatementImpl(CONDITION_STATEMENT)
                      DevInCaseConditionImpl(CASE_CONDITION)
                        PsiElement(DevInTokenType.QUOTE_STRING)('"variable-success"')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.{)('{')
                      PsiWhiteSpace(' ')
                      DevInIneqComparisonExprImpl(INEQ_COMPARISON_EXPR)
                        DevInRefExprImpl(REF_EXPR)
                          DevInLiteralExprImpl(LITERAL_EXPR)
                            PsiElement(VARIABLE_START)('$')
                            PsiElement(DevInTokenType.IDENTIFIER)('selection')
                          PsiElement(DevInTokenType..)('.')
                          PsiElement(DevInTokenType.IDENTIFIER)('length')
                        PsiWhiteSpace(' ')
                        DevInIneqComparisonOpImpl(INEQ_COMPARISON_OP)
                          PsiElement(DevInTokenType.>)('>')
                        PsiWhiteSpace(' ')
                        DevInLiteralExprImpl(LITERAL_EXPR)
                          PsiElement(DevInTokenType.NUMBER)('1')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.})('}')
                      PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('      ')
                    DevInConditionStatementImpl(CONDITION_STATEMENT)
                      DevInCaseConditionImpl(CASE_CONDITION)
                        PsiElement(DevInTokenType.QUOTE_STRING)('"jsonpath-success"')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.{)('{')
                      PsiWhiteSpace(' ')
                      DevInCallExprImpl(CALL_EXPR)
                        DevInRefExprImpl(REF_EXPR)
                          PsiElement(DevInTokenType.IDENTIFIER)('jsonpath')
                        PsiElement(DevInTokenType.()('(')
                        DevInExpressionListImpl(EXPRESSION_LIST)
                          DevInLiteralExprImpl(LITERAL_EXPR)
                            PsiElement(DevInTokenType.QUOTE_STRING)('"/bookstore/book[price>35]"')
                        PsiElement(DevInTokenType.))(')')
                      PsiWhiteSpace(' ')
                      PsiElement(DevInTokenType.})('}')
                      PsiElement(DevInTokenType.NEWLINE)('\n')
                    PsiWhiteSpace('    ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  PsiElement(DevInTokenType.case)('case')
                  PsiWhiteSpace(' ')
                  PsiElement(DevInTokenType.condition)('condition')
                  PsiWhiteSpace(' ')
                  PsiElement(DevInTokenType.{)('{')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('      ')
                  DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                    DevInCaseConditionImpl(CASE_CONDITION)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"variable-sucesss"')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('done')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('      ')
                  DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                    DevInCaseConditionImpl(CASE_CONDITION)
                      PsiElement(DevInTokenType.QUOTE_STRING)('"jsonpath-success"')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('task')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            <empty list>
                          PsiElement(DevInTokenType.))(')')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('      ')
                  DevInCasePatternActionImpl(CASE_PATTERN_ACTION)
                    DevInCaseConditionImpl(CASE_CONDITION)
                      PsiElement(DevInTokenType.default)('default')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.{)('{')
                    PsiWhiteSpace(' ')
                    DevInActionBodyImpl(ACTION_BODY)
                      DevInActionExprImpl(ACTION_EXPR)
                        DevInFuncCallImpl(FUNC_CALL)
                          DevInFuncNameImpl(FUNC_NAME)
                            PsiElement(DevInTokenType.IDENTIFIER)('task')
                          PsiElement(DevInTokenType.()('(')
                          DevInPipelineArgsImpl(PIPELINE_ARGS)
                            <empty list>
                          PsiElement(DevInTokenType.))(')')
                    PsiWhiteSpace(' ')
                    PsiElement(DevInTokenType.})('}')
                    PsiElement(DevInTokenType.NEWLINE)('\n')
                  PsiWhiteSpace('    ')
                  PsiElement(DevInTokenType.})('}')
                  PsiElement(DevInTokenType.NEWLINE)('\n')
          PsiWhiteSpace('  ')
          PsiElement(DevInTokenType.})('}')
          PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.FRONTMATTER_END)('---')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('onStreamingDone:')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('- Array of processor')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('- Object flow')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  CodeBlockElement(CODE)
    PsiElement(DevInTokenType.CODE_BLOCK_START)('```')
    PsiElement(DevInTokenType.LANGUAGE_ID)('markdown')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    ASTWrapperPsiElement(CODE_CONTENTS)
      PsiElement(DevInTokenType.CODE_CONTENT)('- //bookstore/book[price>35]')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('- $.phoneNumbers[:1].type')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('- Regex')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.CODE_BLOCK_END)('```')