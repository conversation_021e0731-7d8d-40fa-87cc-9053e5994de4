---
name: "Context Variable"
description: "Here is a description of the action."
interaction:  RunPanel
variables:
  "currentCode": /HobbitHole\.kt/ { cat }
  "testCode": /ShireCompileTest\.kt/ { cat }
  "actionLocation": /ShireActionLocation\.kt/ { cat }
onStreamingEnd: { append($actionLocation) | saveFile("docs/shire/shire-hobbit-hole.md") }
---

我有一份用户手册写得不好，需要你从用户容易阅读的角度，重新写一份。

根据如下的代码用例、文档，编写对应的 HobbitHole 相关信息的 markdown 文档。

现有代码：

$currentCode

代码用例如下：

$testCode

要求：

1. 尽详细介绍 HobbitHole 的相关信息和示例。
2. 请按现有的文档 Heading 方式编写，并去除非必要的代码。
