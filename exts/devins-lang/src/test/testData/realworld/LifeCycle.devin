---
when: "xxx"
variables:
  "testTemplate": /\(.*\).java/ {
    case "$1" {
      "Controller" { cat(".shire/templates/ControllerTest.java") }
      "Service" { cat(".shire/templates/ServiceTest.java") }
      default  { cat(".shire/templates/DefaultTest.java") }
    }
  }
onStreaming: { /* functions */ }
onStreamingEnd: { parseCode("json") | verifyCode("json") | runCode("json") }
afterStreaming: {
    condition {
      "variable-success" { $selection.length > 1 }
      "jsonpath-success" { jsonpath("/bookstore/book[price>35]") }
    }
    case condition {
      "variable-sucesss" { done }
      "jsonpath-success" { task() }
      default { task() }
    }
  }
---

onStreamingDone:

- Array of processor
- Object flow

```markdown
- //bookstore/book[price>35]
- $.phoneNumbers[:1].type
- Regex
```
