DevInFile
  CodeBlockElement(CODE)
    PsiElement(DevInTokenType.CODE_BLOCK_START)('```')
    PsiElement(DevInTokenType.LANGUAGE_ID)('java')
    PsiElement(DevInTokenType.NEWLINE)('\n')
    ASTWrapperPsiElement(CODE_CONTENTS)
      PsiElement(DevInTokenType.CODE_CONTENT)('@Target({ElementType.TYPE})')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('@Retention(RetentionPolicy.RUNTIME)')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('public @interface ExampleAnnotation {')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('    String value() default "";')
      PsiElement(DevInTokenType.NEWLINE)('\n')
      PsiElement(DevInTokenType.CODE_CONTENT)('}')
      PsiElement(DevInTokenType.NEWLINE)('\n')
    PsiElement(DevInTokenType.CODE_BLOCK_END)('```')