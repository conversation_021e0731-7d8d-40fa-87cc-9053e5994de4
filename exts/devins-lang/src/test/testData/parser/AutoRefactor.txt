DevInFile
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('rename')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' cc.unitmesh.devti.language.run.DevInsProgramRunner to cc.unitmesh.devti.language.run.DevInsProgramRunnerImpl')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('safeDelete')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' cc.unitmesh.devti.language.run.DevInsProgramRunnerImpl')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('delete')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' cc.unitmesh.devti.language.run.DevInsProgramRunnerImpl')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('move')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' cc.unitmesh.devti.language.DevInsProgramRunner to cc.unitmesh.devti.language.run.DevInsProgramRunner')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('refactor')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('extractMethod')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' cc.unitmesh.devti.language.run.DevInsProgramRunnerImpl.run#L20-L30')