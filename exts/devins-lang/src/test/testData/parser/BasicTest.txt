DevInFile
  PsiElement(DevInTokenType.TEXT_SEGMENT)('你好 @hello-world sm')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('解释一下代码')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInVariableStartImpl(VARIABLE_START)
      PsiElement(VARIABLE_START)('$')
    DevInVariableIdImpl(VARIABLE_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('selection')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' 表示选择的内容')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInAgentStartImpl(AGENT_START)
      PsiElement(AGENT_START)('@')
    DevInAgentIdImpl(AGENT_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('agent-name')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' 调用特定的 agent')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('file')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('Sample.file')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' 从文件中读取内容')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    DevInCommandStartImpl(COMMAND_START)
      PsiElement(COMMAND_START)('/')
    DevInCommandIdImpl(COMMAND_ID)
      PsiElement(DevInTokenType.IDENTIFIER)('rev')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('632372da')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' 从版本库中读取内容')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('#system_id:51 传递参数到 story_id')