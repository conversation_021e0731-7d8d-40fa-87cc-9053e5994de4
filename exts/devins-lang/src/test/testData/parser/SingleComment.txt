DevInFile
  PsiElement(DevInTokenType.COMMENTS)('[flow]:flowable.devin')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.COMMENTS)('[flow](result)')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.COMMENTS)('[] is a symbol of comment, follow markdown syntax, and the content in [] is the comment content.')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' The comment content is not displayed in the final result. So we can use it to add some notes to the flow.')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)('[ Normal start')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(']')