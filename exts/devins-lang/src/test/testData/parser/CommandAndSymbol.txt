DevInFile
  DevInUsedImpl(USED)
    PsiElement(DevInTokenType.COMMAND_START)('/')
    PsiElement(DevInTokenType.COMMAND_ID)('explain')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' ')
  DevInUsedImpl(USED)
    PsiElement(DevInTokenType.COMMAND_START)('/')
    PsiElement(DevInTokenType.COMMAND_ID)('symbol')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('cc.unitmesh.devti#RevProvider.constructor')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    PsiElement(DevInTokenType.COMMAND_START)('/')
    PsiElement(DevInTokenType.COMMAND_ID)('refactor')
  PsiElement(DevInTokenType.TEXT_SEGMENT)(' ')
  DevInUsedImpl(USED)
    PsiElement(DevInTokenType.COMMAND_START)('/')
    PsiElement(DevInTokenType.COMMAND_ID)('symbol')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('cc.unitmesh.devti#RevProvider.completions')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  PsiElement(DevInTokenType.NEWLINE)('\n')
  DevInUsedImpl(USED)
    PsiElement(DevInTokenType.COMMAND_START)('/')
    PsiElement(DevInTokenType.COMMAND_ID)('write')
    PsiElement(DevInTokenType.COLON)(':')
    PsiElement(DevInTokenType.COMMAND_PROP)('presentation/VirtualFilePresentation.java')
    PsiElement(DevInTokenType.SHARP)('#')
    PsiElement(DevInTokenType.LINE_INFO)('L1-L12')