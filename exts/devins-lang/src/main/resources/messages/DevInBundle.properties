devin.ref.loading=Loading git revision
devin.line.marker.run.0=Run {0}
devin.label.choose.file=Choice DevIn File
devin.run.error.script.not.found=Script not found
devin.patch.cannot.read.patch=Cannot read a patch

inspection.group.name=DevIns language
inspection.duplicate.agent=Duplicate agent calls detected. It is recommended to make only one call per agent. Please remove any duplicate agent calls.
autodev.intention.category=AutoDevIntention
editor.preview.tip=Show Shire Prompt Preview
editor.preview.refresh=Refresh Preview
editor.preview=Show Preview
editor.preview.help=Help
editor.preview.help.url=https://ide.unitmesh.cc/local-agent/
editor.preview.variable.panel=Custom Variable Snapshots
devin.prompt.fix.command=You are a top software developer in the world, which can help me to fix the issue.\nWhen I use shell-like language and compile the script, I got an error, can you help me to fix it?\n\nOrigin script:\n```\n{0}\n```\n\nScript with result:\n####\n{1}\n####
devin.prompt.fix.run-result=You are a top software developer in the world, which can help me to fix the issue.\n\nHere is the run result, can you help me to fix it?\nRun result:\n####\n{0}\n####

