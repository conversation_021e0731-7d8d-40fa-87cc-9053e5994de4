<idea-plugin package="cc.unitmesh.terminal">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="org.jetbrains.plugins.terminal"/>
    </dependencies>

    <actions>
        <action id="ShellSuggestionAction"
                class="cc.unitmesh.terminal.ShellCommandSuggestAction"
                description="Suggestions for shell commands"
                text="Shell Command Suggestions"
                icon="cc.unitmesh.devti.AutoDevIcons.AI_COPILOT">

            <add-to-group group-id="TerminalToolwindowActionGroup" anchor="last"/>
        </action>
    </actions>

    <extensions defaultExtensionNs="cc.unitmesh">
        <langSketchProvider implementation="cc.unitmesh.terminal.sketch.TerminalDiffLangSketchProvider"/>
    </extensions>
</idea-plugin>
