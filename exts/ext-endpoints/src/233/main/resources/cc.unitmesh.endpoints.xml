<idea-plugin package="cc.unitmesh.endpoints">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="com.intellij.microservices.ui"/>
        <plugin id="com.intellij.spring"/>
        <plugin id="com.intellij.spring.mvc"/>
    </dependencies>

    <extensions defaultExtensionNs="cc.unitmesh">
<!--  Since it's very slow to load all the endpoints, we disable it by default. -->
<!--        <chatContextProvider implementation="cc.unitmesh.endpoints.provider.EndpointsContextProvider"/>-->
        <toolchainFunctionProvider implementation="cc.unitmesh.endpoints.bridge.WebApiViewFunctionProvider"/>

        <knowledgeWebApiProvide implementation="cc.unitmesh.endpoints.bridge.EndpointKnowledgeWebApiProvider"/>
    </extensions>
</idea-plugin>
