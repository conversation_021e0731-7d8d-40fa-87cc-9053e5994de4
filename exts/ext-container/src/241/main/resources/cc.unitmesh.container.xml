<idea-plugin package="cc.unitmesh.container">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="org.jetbrains.plugins.docker.gateway"/>
        <plugin id="Docker"/>
    </dependencies>

    <!--  Since limit of Docker, we move implementation to docker-contrib.xml  -->
    <extensions defaultExtensionNs="cc.unitmesh">
        <runService implementation="cc.unitmesh.container.RunDockerfileService"/>
        <runService implementation="cc.unitmesh.container.RunDevContainerService"/>
    </extensions>
</idea-plugin>
